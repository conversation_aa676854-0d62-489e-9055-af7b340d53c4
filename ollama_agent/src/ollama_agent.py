#!/usr/bin/env python3
"""
Agno Agent with Ollama and Llama 3.2
=====================================

This script demonstrates how to create an Agno Level 2 Agent using:
- Ollama with Llama 3.2 model (free, local)
- Local SQLite storage for session management
- Simple knowledge base without requiring API keys

Requirements:
- Ollama installed and running
- llama3.2 model pulled via ollama
- agno package installed
"""

from agno.agent import Agent
from agno.models.ollama import Ollama
from agno.storage.sqlite import SqliteStorage
from agno.knowledge.text import TextKnowledge
from agno.vectordb.lancedb import LanceDb
from agno.embedder.sentence_transformer import SentenceTransformerEmbedder
import os

def create_knowledge_base():
    """Create a simple knowledge base with local embeddings (no API keys needed)"""
    
    # Sample knowledge content about Python and AI
    knowledge_content = """
    Python Programming Basics:
    Python is a high-level, interpreted programming language known for its simplicity and readability.
    Key features include dynamic typing, automatic memory management, and extensive standard library.
    
    Artificial Intelligence:
    AI is the simulation of human intelligence in machines programmed to think and learn.
    Machine learning is a subset of AI that enables systems to learn from data.
    
    Ollama:
    Ollama is a tool for running large language models locally on your machine.
    It supports various open-source models like Llama, Mistral, and others.
    
    Agno Framework:
    Agno is a full-stack framework for building Multi-Agent Systems with memory, knowledge and reasoning.
    It supports 5 levels of agentic systems from basic agents to complex workflows.
    """
    
    # Create knowledge base with local embeddings
    knowledge = TextKnowledge(
        text=knowledge_content,
        vector_db=LanceDb(
            uri="tmp/lancedb_ollama",
            table_name="ollama_knowledge",
            # Use local sentence transformer embeddings (no API key needed)
            embedder=SentenceTransformerEmbedder(
                model="all-MiniLM-L6-v2",  # Small, fast model
                dimensions=384
            ),
        ),
    )
    
    return knowledge

def create_agent():
    """Create an Agno agent with Ollama and Llama 3.2"""
    
    # Create knowledge base
    knowledge = create_knowledge_base()
    
    # Create local storage
    storage = SqliteStorage(
        table_name="ollama_agent_sessions", 
        db_file="tmp/ollama_agent.db"
    )
    
    # Create agent with Ollama model
    agent = Agent(
        name="Ollama Assistant",
        model=Ollama(
            id="llama3.2",  # Make sure this model is pulled via ollama
            # You can also try other models like:
            # id="llama3.2:1b"  # Smaller version
            # id="llama3.2:3b"  # Medium version
        ),
        instructions=[
            "You are a helpful AI assistant powered by Llama 3.2 running locally via Ollama.",
            "Search your knowledge base before answering questions when relevant.",
            "Be concise but informative in your responses.",
            "If you don't know something, say so honestly.",
        ],
        knowledge=knowledge,
        storage=storage,
        # Add session management
        add_history_to_messages=True,
        num_history_runs=3,
        # Enable markdown for better formatting
        markdown=True,
        # Show when the agent is thinking
        show_tool_calls=True,
    )
    
    return agent

def main():
    """Main function to run the Ollama agent"""
    
    print("🚀 Starting Agno Agent with Ollama and Llama 3.2...")
    print("=" * 60)
    
    # Check if ollama is running
    try:
        import subprocess
        result = subprocess.run(["ollama", "list"], capture_output=True, text=True)
        if "llama3.2" not in result.stdout:
            print("⚠️  Warning: llama3.2 model not found!")
            print("Please run: ollama pull llama3.2")
            print("Available models:")
            print(result.stdout)
            return
    except FileNotFoundError:
        print("❌ Error: Ollama not found. Please install Ollama first.")
        print("Visit: https://ollama.com")
        return
    
    # Create the agent
    agent = create_agent()
    
    # Load knowledge base (only needed on first run)
    print("📚 Loading knowledge base...")
    try:
        agent.knowledge.load(recreate=False)
        print("✅ Knowledge base loaded successfully!")
    except Exception as e:
        print(f"⚠️  Warning: Could not load knowledge base: {e}")
        print("The agent will still work without the knowledge base.")
    
    print("\n🤖 Agent ready! You can now chat with your local Llama 3.2 model.")
    print("Type 'quit', 'exit', or 'bye' to stop the conversation.")
    print("=" * 60)
    
    # Interactive chat loop
    while True:
        try:
            user_input = input("\n👤 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'bye', 'q']:
                print("\n👋 Goodbye! Thanks for using the Ollama agent.")
                break
            
            if not user_input:
                continue
            
            print("\n🤖 Assistant:")
            print("-" * 40)
            
            # Get response from agent
            agent.print_response(user_input, stream=True)
            
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye! Thanks for using the Ollama agent.")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            print("Please try again or type 'quit' to exit.")

def demo_questions():
    """Run some demo questions to test the agent"""
    
    print("🧪 Running demo questions...")
    agent = create_agent()
    
    # Load knowledge base
    try:
        agent.knowledge.load(recreate=False)
    except:
        pass
    
    demo_questions = [
        "What is Python programming?",
        "Tell me about Ollama",
        "What is the Agno framework?",
        "How does machine learning work?",
        "Write a simple Python function to add two numbers",
    ]
    
    for i, question in enumerate(demo_questions, 1):
        print(f"\n📝 Demo Question {i}: {question}")
        print("-" * 50)
        agent.print_response(question, stream=True)
        print("\n" + "=" * 60)

if __name__ == "__main__":
    import sys
    
    # Create tmp directory if it doesn't exist
    os.makedirs("tmp", exist_ok=True)
    
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        demo_questions()
    else:
        main()
