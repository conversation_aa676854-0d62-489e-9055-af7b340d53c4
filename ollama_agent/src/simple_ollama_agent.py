#!/usr/bin/env python3
"""
Simple Agno Agent with Ollama and Llama 3.2
============================================

A minimal example that works with just Agno and Ollama.
No heavy dependencies like torch or sentence-transformers required.
"""

from agno.agent import Agent
from agno.models.ollama import Ollama
from agno.storage.sqlite import SqliteStorage
import os

def create_simple_agent():
    """Create a simple Agno agent with Ollama and Llama 3.2"""
    
    # Create local storage
    storage = SqliteStorage(
        table_name="simple_ollama_sessions", 
        db_file="tmp/simple_ollama.db"
    )
    
    # Create agent with Ollama model
    agent = Agent(
        name="Simple Ollama Assistant",
        model=Ollama(
            id="llama3.2",  # Make sure this model is pulled via ollama
        ),
        instructions=[
            "You are a helpful AI assistant powered by Llama 3.2 running locally via Ollama.",
            "Be concise but informative in your responses.",
            "If you don't know something, say so honestly.",
            "You are running completely locally with no external API calls.",
        ],
        storage=storage,
        # Add session management
        add_history_to_messages=True,
        num_history_runs=3,
        # Enable markdown for better formatting
        markdown=True,
        # Show when the agent is thinking
        show_tool_calls=True,
    )
    
    return agent

def main():
    """Main function to run the simple Ollama agent"""
    
    print("🚀 Starting Simple Agno Agent with Ollama and Llama 3.2...")
    print("=" * 60)
    
    # Check if ollama is running
    try:
        import subprocess
        result = subprocess.run(["ollama", "list"], capture_output=True, text=True)
        if "llama3.2" not in result.stdout:
            print("⚠️  Warning: llama3.2 model not found!")
            print("Please run: ollama pull llama3.2")
            print("Available models:")
            print(result.stdout)
            return
        else:
            print("✅ Llama 3.2 model found!")
    except FileNotFoundError:
        print("❌ Error: Ollama not found. Please install Ollama first.")
        print("Visit: https://ollama.com")
        return
    
    # Create the agent
    agent = create_simple_agent()
    
    print("\n🤖 Simple Agent ready! You can now chat with your local Llama 3.2 model.")
    print("Type 'quit', 'exit', or 'bye' to stop the conversation.")
    print("=" * 60)
    
    # Interactive chat loop
    while True:
        try:
            user_input = input("\n👤 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'bye', 'q']:
                print("\n👋 Goodbye! Thanks for using the Simple Ollama agent.")
                break
            
            if not user_input:
                continue
            
            print("\n🤖 Assistant:")
            print("-" * 40)
            
            # Get response from agent
            agent.print_response(user_input, stream=True)
            
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye! Thanks for using the Simple Ollama agent.")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            print("Please try again or type 'quit' to exit.")

def demo_questions():
    """Run some demo questions to test the agent"""
    
    print("🧪 Running demo questions...")
    agent = create_simple_agent()
    
    demo_questions = [
        "Hello! Can you introduce yourself?",
        "What is Python programming?",
        "Write a simple Python function to calculate factorial",
        "What are the benefits of running AI models locally?",
        "Explain what Ollama is in simple terms",
    ]
    
    for i, question in enumerate(demo_questions, 1):
        print(f"\n📝 Demo Question {i}: {question}")
        print("-" * 50)
        try:
            agent.print_response(question, stream=True)
        except Exception as e:
            print(f"Error: {e}")
        print("\n" + "=" * 60)

if __name__ == "__main__":
    import sys
    
    # Create tmp directory if it doesn't exist
    os.makedirs("tmp", exist_ok=True)
    
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        demo_questions()
    else:
        main()
