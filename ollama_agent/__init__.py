"""
Ollama Agent - Local AI Assistant
=================================

A Level 2 Agno agent with knowledge base and storage, running completely
locally using Ollama and Llama 3.2.

Example:
    from ollama_agent.src.simple_ollama_agent import create_simple_agent
    
    agent = create_simple_agent()
    response = agent.run("What is Python programming?")
"""

__version__ = "1.0.0"
__author__ = "Agno AI Agents"
__description__ = "Local AI Assistant with Ollama and Llama 3.2"
