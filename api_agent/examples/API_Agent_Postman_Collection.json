{"info": {"name": "API Agent Testing Collection", "description": "Complete collection for testing both the Service Desk API and the intelligent API Agent", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "service_desk_url", "value": "http://*************", "type": "string"}, {"key": "agent_server_url", "value": "http://localhost:5000", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}], "item": [{"name": "Service Desk API", "item": [{"name": "1. Authentication", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('access_token', response.access_token);", "    console.log('Access token saved:', response.access_token);", "    pm.test('Authentication successful', function () {", "        pm.expect(response.access_token).to.not.be.undefined;", "    });", "} else {", "    pm.test('Authentication failed', function () {", "        pm.response.to.have.status(200);", "    });", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Basic ZmxvdG8td2ViLWFwcDpjN3ByZE5KRVdFQmt4NGw3ZmV6bA==", "type": "text"}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Accept", "value": "*/*", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "username", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "2d7QdRn6bMI1Q2vQBhficw==", "type": "text"}, {"key": "grant_type", "value": "password", "type": "text"}]}, "url": {"raw": "{{service_desk_url}}/api/oauth/token", "host": ["{{service_desk_url}}"], "path": ["api", "o<PERSON>h", "token"]}}}, {"name": "2. Search High Priority Open Requests", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json, text/plain, */*", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"qualDetails\": {\n    \"type\": \"FlatQualificationRest\",\n    \"quals\": [\n      {\n        \"type\": \"RelationalQualificationRest\",\n        \"leftOperand\": {\n          \"type\": \"PropertyOperandRest\",\n          \"key\": \"request.statusId\"\n        },\n        \"operator\": \"not_in\",\n        \"rightOperand\": {\n          \"type\": \"ValueOperandRest\",\n          \"value\": {\n            \"type\": \"ListLongValueRest\",\n            \"value\": [13]\n          }\n        }\n      },\n      {\n        \"type\": \"RelationalQualificationRest\",\n        \"leftOperand\": {\n          \"type\": \"PropertyOperandRest\",\n          \"key\": \"request.urgencyId\"\n        },\n        \"operator\": \"equals\",\n        \"rightOperand\": {\n          \"type\": \"ValueOperandRest\",\n          \"value\": {\n            \"type\": \"LongValueRest\",\n            \"value\": 3\n          }\n        }\n      }\n    ]\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{service_desk_url}}/api/request/search/byqual?offset=0&size=25&sort_by=createdTime", "host": ["{{service_desk_url}}"], "path": ["api", "request", "search", "byqual"], "query": [{"key": "offset", "value": "0"}, {"key": "size", "value": "25"}, {"key": "sort_by", "value": "createdTime"}]}}}, {"name": "3. Get Request Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json, text/plain, */*", "type": "text"}], "url": {"raw": "{{service_desk_url}}/api/request/1", "host": ["{{service_desk_url}}"], "path": ["api", "request", "1"]}}}, {"name": "4. Create New Request", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json, text/plain, */*", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"linkAssetIds\": [],\n  \"linkCiIds\": [],\n  \"requester\": \"AutoMind\",\n  \"subject\": \"Test Request from Postman\",\n  \"statusId\": 9,\n  \"urgencyId\": 3,\n  \"impactId\": 3,\n  \"requesterId\": 1,\n  \"captchaInfo\": {}\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{service_desk_url}}/api/request", "host": ["{{service_desk_url}}"], "path": ["api", "request"]}}}]}, {"name": "API Agent Server", "item": [{"name": "1. Health Check", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{agent_server_url}}/health", "host": ["{{agent_server_url}}"], "path": ["health"]}}}, {"name": "2. Check Authentication Status", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{agent_server_url}}/auth-status", "host": ["{{agent_server_url}}"], "path": ["auth-status"]}}}, {"name": "3. Manual Authentication", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{agent_server_url}}/authenticate", "host": ["{{agent_server_url}}"], "path": ["authenticate"]}}}, {"name": "4. Get Examples", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{agent_server_url}}/examples", "host": ["{{agent_server_url}}"], "path": ["examples"]}}}, {"name": "5. Query - High Priority Open Requests", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"Show me high priority open requests\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{agent_server_url}}/query", "host": ["{{agent_server_url}}"], "path": ["query"]}}}, {"name": "6. Query - Get Request Details", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"Get details of request ID 1\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{agent_server_url}}/query", "host": ["{{agent_server_url}}"], "path": ["query"]}}}, {"name": "7. Query - Create New Request", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"Create a new request for printer issue with high priority\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{agent_server_url}}/query", "host": ["{{agent_server_url}}"], "path": ["query"]}}}, {"name": "8. Query - <PERSON> Requests", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"query\": \"Find all urgent requests\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{agent_server_url}}/query", "host": ["{{agent_server_url}}"], "path": ["query"]}}}]}]}