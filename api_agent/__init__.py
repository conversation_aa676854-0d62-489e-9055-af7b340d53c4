"""
API Agent - Natural Language to API Translation
==============================================

An intelligent agent that understands natural language queries and automatically
translates them into API calls for service desk systems.

Example:
    from api_agent.src.api_agent import create_api_agent
    
    agent = create_api_agent()
    response = agent.run("Show me high priority open requests")
"""

__version__ = "1.0.0"
__author__ = "Agno AI Agents"
__description__ = "Natural Language to API Translation Agent"
