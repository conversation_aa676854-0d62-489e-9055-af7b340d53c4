#!/usr/bin/env python3
"""
API Agent with Natural Language to API Translation
==================================================

This agent can understand natural language requests and automatically:
1. Determine which API endpoint to call
2. Generate the correct payload
3. Execute the API call
4. Return formatted results

Example usage:
- "Show me requests with status open and high priority"
- "Create a new request for printer issue"
- "Get details of request ID 5"
"""

import json
import requests
from typing import Dict, Any, Optional, List
from agno.agent import Agent
from agno.models.ollama import Ollama
from agno.storage.sqlite import SqliteStorage
from agno.tools import Toolkit
import os
import urllib3

# Disable SSL warnings for internal APIs
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class ServiceDeskAPI:
    """Service Desk API client based on your curl examples"""
    
    def __init__(self, base_url: str = "http://172.16.15.113"):
        self.base_url = base_url
        self.token = None
        self.headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-GB,en;q=0.9',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
        }
        
        # Status and priority mappings (you may need to adjust these)
        self.status_mapping = {
            'open': [9],
            'in_progress': [10],
            'pending': [11],
            'resolved': [12],
            'closed': [13]
        }
        
        self.priority_mapping = {
            'low': 1,
            'medium': 2, 
            'high': 3,
            'urgent': 4
        }
        
        self.impact_mapping = {
            'low': 1,
            'medium': 2,
            'high': 3,
            'critical': 4
        }

    def authenticate(self, username: str = "<EMAIL>", password: str = "2d7QdRn6bMI1Q2vQBhficw=="):
        """Authenticate and get access token"""
        auth_url = f"{self.base_url}/api/oauth/token"
        
        auth_headers = {
            'Authorization': 'Basic ZmxvdG8td2ViLWFwcDpjN3ByZE5KRVdFQmt4NGw3ZmV6bA==',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        data = f'username={username}&password={password}&grant_type=password'
        
        try:
            response = requests.post(auth_url, headers=auth_headers, data=data, verify=False)
            if response.status_code == 200:
                token_data = response.json()
                self.token = token_data.get('access_token')
                self.headers['Authorization'] = f'Bearer {self.token}'
                return True
            else:
                print(f"Authentication failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"Authentication error: {e}")
            return False

    def search_requests(self, status_filter: List[str] = None, priority: str = None, 
                       impact: str = None, offset: int = 0, size: int = 25) -> Dict[str, Any]:
        """Search requests with filters"""
        if not self.token:
            if not self.authenticate():
                return {"error": "Authentication failed"}
        
        url = f"{self.base_url}/api/request/search/byqual"
        params = {
            'offset': offset,
            'size': size,
            'sort_by': 'createdTime'
        }
        
        # Build qualification filters
        quals = []
        
        # Status filter
        if status_filter:
            status_ids = []
            for status in status_filter:
                status_ids.extend(self.status_mapping.get(status.lower(), []))
            
            if status_ids:
                if 'open' in [s.lower() for s in status_filter]:
                    # For open requests, exclude closed status
                    quals.append({
                        "type": "RelationalQualificationRest",
                        "leftOperand": {"type": "PropertyOperandRest", "key": "request.statusId"},
                        "operator": "not_in",
                        "rightOperand": {"type": "ValueOperandRest", "value": {"type": "ListLongValueRest", "value": [13]}}
                    })
                else:
                    quals.append({
                        "type": "RelationalQualificationRest",
                        "leftOperand": {"type": "PropertyOperandRest", "key": "request.statusId"},
                        "operator": "in",
                        "rightOperand": {"type": "ValueOperandRest", "value": {"type": "ListLongValueRest", "value": status_ids}}
                    })
        
        # Priority filter
        if priority:
            priority_id = self.priority_mapping.get(priority.lower())
            if priority_id:
                quals.append({
                    "type": "RelationalQualificationRest",
                    "leftOperand": {"type": "PropertyOperandRest", "key": "request.urgencyId"},
                    "operator": "equals",
                    "rightOperand": {"type": "ValueOperandRest", "value": {"type": "LongValueRest", "value": priority_id}}
                })
        
        payload = {
            "qualDetails": {
                "type": "FlatQualificationRest",
                "quals": quals
            }
        }
        
        try:
            response = requests.post(url, headers=self.headers, json=payload, params=params, verify=False)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"Search failed: {response.status_code}", "details": response.text}
        except Exception as e:
            return {"error": f"Search error: {e}"}

    def get_request(self, request_id: int) -> Dict[str, Any]:
        """Get specific request by ID"""
        if not self.token:
            if not self.authenticate():
                return {"error": "Authentication failed"}
        
        url = f"{self.base_url}/api/request/{request_id}"
        
        try:
            response = requests.get(url, headers=self.headers, verify=False)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"Request not found: {response.status_code}"}
        except Exception as e:
            return {"error": f"Get request error: {e}"}

    def create_request(self, subject: str, requester: str = "AutoMind", 
                      priority: str = "medium", impact: str = "medium", 
                      description: str = None) -> Dict[str, Any]:
        """Create a new request"""
        if not self.token:
            if not self.authenticate():
                return {"error": "Authentication failed"}
        
        url = f"{self.base_url}/api/request"
        
        urgency_id = self.priority_mapping.get(priority.lower(), 2)
        impact_id = self.impact_mapping.get(impact.lower(), 2)
        
        payload = {
            "linkAssetIds": [],
            "linkCiIds": [],
            "requester": requester,
            "subject": subject,
            "statusId": 9,  # New status
            "urgencyId": urgency_id,
            "impactId": impact_id,
            "requesterId": 1,
            "captchaInfo": {}
        }
        
        if description:
            payload["description"] = description
        
        try:
            response = requests.post(url, headers=self.headers, json=payload, verify=False)
            if response.status_code in [200, 201]:
                return response.json()
            else:
                return {"error": f"Create failed: {response.status_code}", "details": response.text}
        except Exception as e:
            return {"error": f"Create error: {e}"}


class APIAgentToolkit(Toolkit):
    """Toolkit for API operations"""
    
    def __init__(self):
        super().__init__(name="api_toolkit")
        self.api = ServiceDeskAPI()
        self.register(self.search_requests)
        self.register(self.get_request_details)
        self.register(self.create_new_request)
    
    def search_requests(self, status: str = None, priority: str = None, limit: int = 10) -> str:
        """
        Search for requests based on status and priority.
        
        Args:
            status: Request status (open, closed, new, in_progress, pending, resolved)
            priority: Priority level (low, medium, high, critical, urgent)
            limit: Maximum number of results to return
        
        Returns:
            Formatted search results
        """
        status_filter = [status] if status else None
        result = self.api.search_requests(status_filter=status_filter, priority=priority, size=limit)
        
        if "error" in result:
            return f"Error searching requests: {result['error']}"
        
        # Format results
        if "content" in result and result["content"]:
            formatted = "Found requests:\n"
            for req in result["content"]:
                formatted += f"- ID: {req.get('id', 'N/A')}, Subject: {req.get('subject', 'N/A')}, Status: {req.get('statusId', 'N/A')}\n"
            return formatted
        else:
            return "No requests found matching the criteria."
    
    def get_request_details(self, request_id: int) -> str:
        """
        Get detailed information about a specific request.
        
        Args:
            request_id: The ID of the request to retrieve
        
        Returns:
            Formatted request details
        """
        result = self.api.get_request(request_id)
        
        if "error" in result:
            return f"Error getting request: {result['error']}"
        
        # Format request details
        formatted = f"Request Details:\n"
        formatted += f"ID: {result.get('id', 'N/A')}\n"
        formatted += f"Subject: {result.get('subject', 'N/A')}\n"
        formatted += f"Requester: {result.get('requester', 'N/A')}\n"
        formatted += f"Status ID: {result.get('statusId', 'N/A')}\n"
        formatted += f"Priority ID: {result.get('urgencyId', 'N/A')}\n"
        formatted += f"Impact ID: {result.get('impactId', 'N/A')}\n"
        formatted += f"Created: {result.get('createdTime', 'N/A')}\n"
        
        return formatted
    
    def create_new_request(self, subject: str, priority: str = "medium", description: str = None) -> str:
        """
        Create a new service request.
        
        Args:
            subject: The subject/title of the request
            priority: Priority level (low, medium, high, critical)
            description: Optional description of the request
        
        Returns:
            Result of request creation
        """
        result = self.api.create_request(subject=subject, priority=priority, description=description)
        
        if "error" in result:
            return f"Error creating request: {result['error']}"
        
        return f"Request created successfully! ID: {result.get('id', 'N/A')}, Subject: {subject}"


def create_api_agent():
    """Create the API agent with natural language understanding"""
    
    # Create storage
    storage = SqliteStorage(
        table_name="api_agent_sessions", 
        db_file="tmp/api_agent.db"
    )
    
    # Create toolkit
    toolkit = APIAgentToolkit()
    
    # Create agent
    agent = Agent(
        name="API Assistant",
        model=Ollama(id="llama3.2"),
        instructions=[
            "You are an API assistant that helps users interact with a service desk system.",
            "You can understand natural language requests and translate them into API calls.",
            "When users ask about requests, tickets, or service desk operations, use the available tools.",
            "Always be helpful and explain what you're doing.",
            "For status filters, use: open, closed, new, in_progress, pending, resolved",
            "For priority levels, use: low, medium, high, critical, urgent",
            "If a user asks for 'high priority' requests, use priority='high'",
            "If a user asks for 'open' requests, use status='open'",
            "Always format your responses clearly and provide useful information."
        ],
        tools=[toolkit],
        storage=storage,
        add_history_to_messages=True,
        num_history_runs=3,
        markdown=True,
        show_tool_calls=True,
    )
    
    return agent


def main():
    """Main function to run the API agent"""
    
    print("🚀 Starting API Agent with Natural Language Understanding...")
    print("=" * 60)
    print("Examples of what you can ask:")
    print("- 'Show me requests with status open and high priority'")
    print("- 'Get details of request ID 5'")
    print("- 'Create a new request for printer issue with high priority'")
    print("- 'Find all closed requests'")
    print("=" * 60)
    
    # Create the agent
    agent = create_api_agent()
    
    print("\n🤖 API Agent ready! Ask me about service desk requests.")
    print("Type 'quit', 'exit', or 'bye' to stop.")
    print("=" * 60)
    
    # Interactive chat loop
    while True:
        try:
            user_input = input("\n👤 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'bye', 'q']:
                print("\n👋 Goodbye! Thanks for using the API agent.")
                break
            
            if not user_input:
                continue
            
            print("\n🤖 Assistant:")
            print("-" * 40)
            
            # Get response from agent
            agent.print_response(user_input, stream=True)
            
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye! Thanks for using the API agent.")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            print("Please try again or type 'quit' to exit.")


if __name__ == "__main__":
    # Create tmp directory if it doesn't exist
    os.makedirs("tmp", exist_ok=True)
    main()
