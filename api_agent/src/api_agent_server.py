#!/usr/bin/env python3
"""
API Agent HTTP Server
====================

A simple Flask server that wraps the API agent so you can test it via HTTP requests
from Postman or any HTTP client.
"""

from flask import Flask, request, jsonify
from api_agent import create_api_agent
import os
import sys
import traceback

# Add the current directory to Python path so we can import api_agent
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

app = Flask(__name__)

# Create the agent once when the server starts
print("🚀 Initializing API Agent...")
agent = create_api_agent()
print("✅ API Agent ready!")

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "message": "API Agent server is running",
        "agent": "ready"
    })

@app.route('/query', methods=['POST'])
def query_agent():
    """
    Main endpoint to send natural language queries to the agent
    
    Expected JSON body:
    {
        "query": "Show me high priority open requests"
    }
    """
    try:
        # Get the query from request body
        data = request.get_json()
        
        if not data or 'query' not in data:
            return jsonify({
                "error": "Missing 'query' field in request body",
                "example": {
                    "query": "Show me high priority open requests"
                }
            }), 400
        
        user_query = data['query']
        
        if not user_query.strip():
            return jsonify({
                "error": "Query cannot be empty"
            }), 400
        
        print(f"📝 Processing query: {user_query}")
        
        # Get response from agent (capture the output)
        import io
        import sys
        from contextlib import redirect_stdout
        
        # Capture the agent's output
        output_buffer = io.StringIO()
        
        try:
            with redirect_stdout(output_buffer):
                # Use the agent's run method to get response
                response = agent.run(user_query)
            
            agent_response = response.content if hasattr(response, 'content') else str(response)
            
            return jsonify({
                "query": user_query,
                "response": agent_response,
                "status": "success"
            })
            
        except Exception as agent_error:
            print(f"❌ Agent error: {agent_error}")
            return jsonify({
                "query": user_query,
                "error": f"Agent processing error: {str(agent_error)}",
                "status": "agent_error"
            }), 500
            
    except Exception as e:
        print(f"❌ Server error: {e}")
        traceback.print_exc()
        return jsonify({
            "error": f"Server error: {str(e)}",
            "status": "server_error"
        }), 500

@app.route('/examples', methods=['GET'])
def get_examples():
    """Get example queries you can try"""
    return jsonify({
        "examples": [
            {
                "query": "Show me high priority open requests",
                "description": "Search for open requests with high priority"
            },
            {
                "query": "Get details of request ID 1",
                "description": "Get detailed information about a specific request"
            },
            {
                "query": "Create a new request for printer issue with high priority",
                "description": "Create a new service request"
            },
            {
                "query": "Find all urgent requests",
                "description": "Search for requests with urgent priority"
            },
            {
                "query": "Show me closed requests",
                "description": "Search for closed requests"
            }
        ],
        "usage": {
            "endpoint": "/query",
            "method": "POST",
            "body": {
                "query": "Your natural language query here"
            }
        }
    })

@app.route('/', methods=['GET'])
def home():
    """Home page with usage instructions"""
    return jsonify({
        "message": "API Agent HTTP Server",
        "version": "1.0.0",
        "endpoints": {
            "GET /": "This help message",
            "GET /health": "Health check",
            "GET /examples": "Example queries",
            "POST /query": "Send natural language query to agent"
        },
        "usage": {
            "1": "Send POST request to /query with JSON body: {'query': 'your question'}",
            "2": "Check /examples for sample queries",
            "3": "Use /health to verify server status"
        },
        "example_curl": "curl -X POST http://localhost:5000/query -H 'Content-Type: application/json' -d '{\"query\": \"Show me high priority open requests\"}'"
    })

if __name__ == '__main__':
    # Create tmp directory if it doesn't exist
    os.makedirs("tmp", exist_ok=True)
    
    print("🌐 Starting API Agent HTTP Server...")
    print("📡 Server will be available at: http://localhost:5000")
    print("📚 Visit http://localhost:5000/examples for example queries")
    print("🔍 Use POST /query to send natural language queries")
    
    # Run the Flask server
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
