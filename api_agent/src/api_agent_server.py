#!/usr/bin/env python3
"""
API Agent HTTP Server
====================

A simple Flask server that wraps the API agent so you can test it via HTTP requests
from Postman or any HTTP client.
"""

from flask import Flask, request, jsonify
from api_agent import create_api_agent
import os
import sys
import traceback

# Add the current directory to Python path so we can import api_agent
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

app = Flask(__name__)

# Create the agent once when the server starts
print("🚀 Initializing API Agent...")
agent = create_api_agent()
print("✅ API Agent ready!")

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "message": "API Agent server is running",
        "agent": "ready"
    })

@app.route('/auth-status', methods=['GET'])
def auth_status():
    """Check authentication status with service desk API"""
    try:
        # Check if the agent's API client has a valid token
        auth_info = {
            "service_desk_url": "http://172.16.15.113",
            "auto_authentication": True,
            "status": "ready",
            "message": "Agent will authenticate automatically when needed"
        }

        # Try to access the API client from the agent's toolkit
        try:
            for tool in agent.tools:
                if hasattr(tool, 'api') and hasattr(tool.api, 'token'):
                    if tool.api.token:
                        auth_info["status"] = "authenticated"
                        auth_info["message"] = "Already authenticated with service desk API"
                        auth_info["token_available"] = True
                    else:
                        auth_info["status"] = "not_authenticated"
                        auth_info["message"] = "Will authenticate on first API call"
                        auth_info["token_available"] = False
                    break
        except Exception as e:
            auth_info["status"] = "ready"
            auth_info["message"] = "Agent ready to authenticate when needed"

        return jsonify(auth_info)

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Error checking authentication status: {str(e)}"
        }), 500

@app.route('/authenticate', methods=['POST'])
def manual_authenticate():
    """Manually trigger authentication with service desk API"""
    try:
        print("🔐 Manual authentication requested...")

        # Try to access the API client from the agent's toolkit and authenticate
        auth_result = {
            "status": "error",
            "message": "Could not access API client"
        }

        try:
            for tool in agent.tools:
                if hasattr(tool, 'api') and hasattr(tool.api, 'authenticate'):
                    print("🔐 Attempting authentication...")
                    success = tool.api.authenticate()
                    if success:
                        auth_result = {
                            "status": "success",
                            "message": "Successfully authenticated with service desk API",
                            "token_available": True
                        }
                        print("✅ Authentication successful!")
                    else:
                        auth_result = {
                            "status": "failed",
                            "message": "Authentication failed - check credentials or network",
                            "token_available": False
                        }
                        print("❌ Authentication failed!")
                    break
        except Exception as e:
            auth_result = {
                "status": "error",
                "message": f"Authentication error: {str(e)}",
                "token_available": False
            }
            print(f"❌ Authentication error: {e}")

        return jsonify(auth_result)

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Error during manual authentication: {str(e)}"
        }), 500

@app.route('/query', methods=['POST'])
def query_agent():
    """
    Main endpoint to send natural language queries to the agent

    Expected JSON body:
    {
        "query": "Show me high priority open requests"
    }

    The agent automatically handles authentication with the service desk API.
    """
    try:
        # Get the query from request body
        data = request.get_json()

        if not data or 'query' not in data:
            return jsonify({
                "error": "Missing 'query' field in request body",
                "example": {
                    "query": "Show me high priority open requests"
                }
            }), 400

        user_query = data['query']

        if not user_query.strip():
            return jsonify({
                "error": "Query cannot be empty"
            }), 400

        print(f"📝 Processing query: {user_query}")
        print("🔐 Agent will automatically handle authentication if needed...")

        # Get response from agent (capture the output)
        import io
        import sys
        from contextlib import redirect_stdout

        # Capture the agent's output
        output_buffer = io.StringIO()

        try:
            with redirect_stdout(output_buffer):
                # Use the agent's run method to get response
                # The agent automatically handles authentication internally
                response = agent.run(user_query)

            agent_response = response.content if hasattr(response, 'content') else str(response)
            
            # Check if authentication was successful by looking at the agent's API client
            auth_status = "unknown"
            try:
                # Access the API client from the agent's toolkit
                for tool in agent.tools:
                    if hasattr(tool, 'api') and hasattr(tool.api, 'token'):
                        auth_status = "authenticated" if tool.api.token else "not_authenticated"
                        break
            except:
                auth_status = "unknown"

            return jsonify({
                "query": user_query,
                "response": agent_response,
                "status": "success",
                "authentication": {
                    "status": auth_status,
                    "message": "Agent automatically handled authentication" if auth_status == "authenticated" else "Authentication status unknown"
                }
            })
            
        except Exception as agent_error:
            print(f"❌ Agent error: {agent_error}")
            return jsonify({
                "query": user_query,
                "error": f"Agent processing error: {str(agent_error)}",
                "status": "agent_error"
            }), 500
            
    except Exception as e:
        print(f"❌ Server error: {e}")
        traceback.print_exc()
        return jsonify({
            "error": f"Server error: {str(e)}",
            "status": "server_error"
        }), 500

@app.route('/examples', methods=['GET'])
def get_examples():
    """Get example queries you can try"""
    return jsonify({
        "examples": [
            {
                "query": "Show me high priority open requests",
                "description": "Search for open requests with high priority"
            },
            {
                "query": "Get details of request ID 1",
                "description": "Get detailed information about a specific request"
            },
            {
                "query": "Create a new request for printer issue with high priority",
                "description": "Create a new service request"
            },
            {
                "query": "Find all urgent requests",
                "description": "Search for requests with urgent priority"
            },
            {
                "query": "Show me closed requests",
                "description": "Search for closed requests"
            }
        ],
        "usage": {
            "endpoint": "/query",
            "method": "POST",
            "body": {
                "query": "Your natural language query here"
            }
        }
    })

@app.route('/', methods=['GET'])
def home():
    """Home page with usage instructions"""
    return jsonify({
        "message": "API Agent HTTP Server",
        "version": "1.0.0",
        "endpoints": {
            "GET /": "This help message",
            "GET /health": "Health check",
            "GET /auth-status": "Check authentication status",
            "POST /authenticate": "Manually trigger authentication",
            "GET /examples": "Example queries",
            "POST /query": "Send natural language query to agent (auto-authenticates)"
        },
        "usage": {
            "1": "Send POST request to /query with JSON body: {'query': 'your question'}",
            "2": "Agent automatically handles authentication when needed",
            "3": "Check /auth-status to see authentication status",
            "4": "Use /authenticate to manually trigger authentication",
            "5": "Check /examples for sample queries"
        },
        "example_curl": "curl -X POST http://localhost:5000/query -H 'Content-Type: application/json' -d '{\"query\": \"Show me high priority open requests\"}'"
    })

if __name__ == '__main__':
    # Create tmp directory if it doesn't exist
    os.makedirs("tmp", exist_ok=True)
    
    print("🌐 Starting API Agent HTTP Server...")
    print("📡 Server will be available at: http://localhost:5000")
    print("📚 Visit http://localhost:5000/examples for example queries")
    print("🔍 Use POST /query to send natural language queries")
    
    # Run the Flask server
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
