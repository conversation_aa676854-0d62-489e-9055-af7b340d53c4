# 🚀 Quick Start Guide - API Agent Server

## ✅ Server Status
**Your API Agent Server is RUNNING at: http://localhost:5000**

## 📱 Postman Setup (5 Minutes)

### Step 1: Import Collection
1. Open Postman
2. Click **Import** button
3. Select file: `api_agent/examples/API_Agent_Postman_Collection.json`
4. Collection "API Agent Testing Collection" will appear

### Step 2: Create Environment
1. Click **Environments** → **Create Environment**
2. Name: "API Agent"
3. Add variables:
   - `agent_server_url`: `http://localhost:5000`
   - `service_desk_url`: `http://*************`
   - `access_token`: (leave empty)
4. **Save** and **Select** this environment

### Step 3: Test Basic Functionality

#### Test 1: Health Check ✅
```
GET http://localhost:5000/health
```
**Expected:** `{"status": "healthy", "agent": "ready"}`

#### Test 2: Get Examples ✅
```
GET http://localhost:5000/examples
```
**Expected:** List of example queries

#### Test 3: Natural Language Query 🤖
```
POST http://localhost:5000/query
Content-Type: application/json

{
  "query": "Show me high priority open requests"
}
```
**Expected:** Agent processes and returns API results

## 🎯 Ready-to-Use Postman Requests

### 1. Health Check
- **Method:** GET
- **URL:** `{{agent_server_url}}/health`
- **Headers:** None needed

### 2. Natural Language Queries
- **Method:** POST  
- **URL:** `{{agent_server_url}}/query`
- **Headers:** `Content-Type: application/json`
- **Body:** `{"query": "YOUR_QUESTION_HERE"}`

## 💬 Example Queries to Try

Copy these into the `query` field:

```json
{"query": "Show me high priority open requests"}
```

```json
{"query": "Get details of request ID 1"}
```

```json
{"query": "Create a new request for printer issue"}
```

```json
{"query": "Find all urgent requests"}
```

```json
{"query": "Show me closed requests"}
```

## 🔐 Authentication Management

### For API Agent (Automatic) ✅
- **No setup needed** - Agent handles authentication automatically
- Uses stored credentials for service desk API
- Manages OAuth tokens transparently
- Refreshes tokens when expired

### For Direct Service Desk API (Manual)
If you want to test the raw API:

1. **Get Token:**
   ```
   POST http://*************/api/oauth/token
   Authorization: Basic ZmxvdG8td2ViLWFwcDpjN3ByZE5KRVdFQmt4NGw3ZmV6bA==
   Content-Type: application/x-www-form-urlencoded
   
   username=<EMAIL>&password=2d7QdRn6bMI1Q2vQBhficw==&grant_type=password
   ```

2. **Use Token:**
   ```
   Authorization: Bearer YOUR_ACCESS_TOKEN_HERE
   ```

## ⏱️ Response Times

- **Health/Examples:** < 1 second
- **Natural Language Queries:** 30-120 seconds (normal for local AI)

## 🔧 Troubleshooting

### Server Issues
```bash
# Check server status
curl http://localhost:5000/health

# Restart if needed
python start_api_server.py
```

### Postman Issues
- Ensure environment is selected
- Check URL variables are set correctly
- Verify Content-Type header for POST requests

### Slow Responses
- **Normal:** 30-120 seconds for AI processing
- **Check:** Server logs show "Thinking..." progress
- **Wait:** Agent is understanding your query and calling APIs

## 📊 What to Expect

### Successful Response
```json
{
  "query": "Show me high priority open requests",
  "response": "Found requests:\n- ID: 1, Subject: Test, Status: 9",
  "status": "success"
}
```

### Error Response
```json
{
  "error": "Missing 'query' field in request body",
  "example": {"query": "Show me high priority open requests"}
}
```

## 🎉 You're Ready!

1. ✅ **Server Running:** http://localhost:5000
2. ✅ **Postman Collection:** Ready to import
3. ✅ **Authentication:** Handled automatically
4. ✅ **Examples:** Ready to test

**Start with the health check, then try natural language queries!**

---

## 🆘 Need Help?

- **Server logs:** Check terminal where you ran `python start_api_server.py`
- **Postman collection:** Import `api_agent/examples/API_Agent_Postman_Collection.json`
- **Documentation:** See `api_agent/docs/README.md`

**Your intelligent API agent is ready to convert natural language to API calls!** 🚀
