# Postman Guide for API Agent Testing

This guide shows you how to test both the underlying service desk API and your intelligent API agent using Postman.

## 🎯 Two Ways to Test

1. **Direct Service Desk API** - Test the raw API endpoints
2. **API Agent Server** - Test the intelligent agent via HTTP

---

## 🔧 Part 1: Testing Direct Service Desk API

### 1.1 Authentication

**Request:**
```
POST http://*************/api/oauth/token
```

**Headers:**
```
Authorization: Basic ZmxvdG8td2ViLWFwcDpjN3ByZE5KRVdFQmt4NGw3ZmV6bA==
Content-Type: application/x-www-form-urlencoded
Accept: */*
Accept-Language: en-GB,en;q=0.9
Connection: keep-alive
User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36
```

**Body (x-www-form-urlencoded):**
```
username=automind%40motadata.com
password=2d7QdRn6bMI1Q2vQBhficw%3D%3D
grant_type=password
```

**Expected Response:**
```json
{
  "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600,
  "scope": "NO-SCOPE"
}
```

### 1.2 Search Open High Priority Requests

**Request:**
```
POST http://*************/api/request/search/byqual?offset=0&size=25&sort_by=createdTime
```

**Headers:**
```
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE
Content-Type: application/json
Accept: application/json, text/plain, */*
Accept-Language: en-GB,en;q=0.9
Connection: keep-alive
User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36
```

**Body (JSON):**
```json
{
  "qualDetails": {
    "type": "FlatQualificationRest",
    "quals": [
      {
        "description": null,
        "qualContext": null,
        "type": "RelationalQualificationRest",
        "leftOperand": {
          "type": "PropertyOperandRest",
          "key": "request.statusId"
        },
        "operator": "not_in",
        "rightOperand": {
          "type": "ValueOperandRest",
          "value": {
            "type": "ListLongValueRest",
            "value": [13]
          }
        }
      },
      {
        "type": "RelationalQualificationRest",
        "leftOperand": {
          "type": "PropertyOperandRest",
          "key": "request.urgencyId"
        },
        "operator": "equals",
        "rightOperand": {
          "type": "ValueOperandRest",
          "value": {
            "type": "LongValueRest",
            "value": 3
          }
        }
      }
    ]
  }
}
```

### 1.3 Get Specific Request

**Request:**
```
GET http://*************/api/request/1
```

**Headers:**
```
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE
Content-Type: application/json
Accept: application/json, text/plain, */*
Accept-Language: en-GB,en;q=0.9
Connection: keep-alive
User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36
```

### 1.4 Create New Request

**Request:**
```
POST http://*************/api/request
```

**Headers:**
```
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE
Content-Type: application/json
Accept: application/json, text/plain, */*
Accept-Language: en-GB,en;q=0.9
Connection: keep-alive
User-Agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36
```

**Body (JSON):**
```json
{
  "linkAssetIds": [],
  "linkCiIds": [],
  "requester": "AutoMind",
  "subject": "Test Request from Postman",
  "statusId": 9,
  "urgencyId": 3,
  "impactId": 3,
  "requesterId": 1,
  "captchaInfo": {}
}
```

---

## 🤖 Part 2: Testing API Agent Server

The API agent server is running at: **http://localhost:5000**

### 2.1 Health Check

**Request:**
```
GET http://localhost:5000/health
```

**Headers:**
```
Content-Type: application/json
```

**Expected Response:**
```json
{
  "status": "healthy",
  "message": "API Agent server is running",
  "agent": "ready"
}
```

### 2.2 Get Example Queries

**Request:**
```
GET http://localhost:5000/examples
```

**Headers:**
```
Content-Type: application/json
```

**Expected Response:**
```json
{
  "examples": [
    {
      "query": "Show me high priority open requests",
      "description": "Search for open requests with high priority"
    },
    {
      "query": "Get details of request ID 1",
      "description": "Get detailed information about a specific request"
    }
  ],
  "usage": {
    "endpoint": "/query",
    "method": "POST",
    "body": {
      "query": "Your natural language query here"
    }
  }
}
```

### 2.3 Send Natural Language Query

**Request:**
```
POST http://localhost:5000/query
```

**Headers:**
```
Content-Type: application/json
```

**Body (JSON) - Example 1:**
```json
{
  "query": "Show me high priority open requests"
}
```

**Body (JSON) - Example 2:**
```json
{
  "query": "Get details of request ID 1"
}
```

**Body (JSON) - Example 3:**
```json
{
  "query": "Create a new request for printer issue with high priority"
}
```

**Body (JSON) - Example 4:**
```json
{
  "query": "Find all urgent requests"
}
```

**Expected Response:**
```json
{
  "query": "Show me high priority open requests",
  "response": "Found requests:\n- ID: 1, Subject: Test, Status: 9\n- ID: 2, Subject: Printer Issue, Status: 2",
  "status": "success"
}
```

---

## 📋 Step-by-Step Postman Setup

### Step 1: Create New Collection
1. Open Postman
2. Click "New" → "Collection"
3. Name it "API Agent Testing"

### Step 2: Add Environment Variables
1. Click "Environments" → "Create Environment"
2. Name it "API Agent"
3. Add variables:
   - `service_desk_url`: `http://*************`
   - `agent_server_url`: `http://localhost:5000`
   - `access_token`: (leave empty, will be set after auth)

### Step 3: Create Requests

#### For Service Desk API:
1. **Auth Request**: POST `{{service_desk_url}}/api/oauth/token`
2. **Search Request**: POST `{{service_desk_url}}/api/request/search/byqual`
3. **Get Request**: GET `{{service_desk_url}}/api/request/1`
4. **Create Request**: POST `{{service_desk_url}}/api/request`

#### For Agent Server:
1. **Health Check**: GET `{{agent_server_url}}/health`
2. **Examples**: GET `{{agent_server_url}}/examples`
3. **Query Agent**: POST `{{agent_server_url}}/query`

### Step 4: Set Up Authentication Script
In the Auth request, add this to the "Tests" tab:
```javascript
if (pm.response.code === 200) {
    const response = pm.response.json();
    pm.environment.set("access_token", response.access_token);
    console.log("Access token saved:", response.access_token);
}
```

### Step 5: Use Token in Other Requests
For service desk API requests, use:
```
Authorization: Bearer {{access_token}}
```

---

## 🧪 Testing Scenarios

### Scenario 1: Basic Agent Test
1. **Health Check**: GET `/health`
2. **Get Examples**: GET `/examples`
3. **Simple Query**: POST `/query` with `{"query": "Show me open requests"}`

### Scenario 2: Natural Language Understanding
Test different ways to ask the same thing:
```json
{"query": "Show me high priority open tickets"}
{"query": "Find open requests with high priority"}
{"query": "Get all urgent open requests"}
{"query": "List high priority requests that are still open"}
```

### Scenario 3: Different Operations
```json
{"query": "Get details of request ID 5"}
{"query": "Create a new request for email not working"}
{"query": "Show me all closed requests"}
{"query": "Find critical priority requests"}
```

### Scenario 4: Error Handling
```json
{"query": ""}
{"query": "Invalid request that makes no sense"}
{}
```

---

## 🔍 Troubleshooting

### Common Issues:

1. **Agent Server Not Running**
   - Check if `python api_agent_server.py` is running
   - Verify port 5000 is available

2. **Service Desk API Connection**
   - Ensure `http://*************` is accessible
   - Check if authentication credentials are correct

3. **SSL/TLS Issues**
   - The agent uses `verify=False` for internal APIs
   - Postman: Turn off "SSL certificate verification"

4. **Token Expiry**
   - Re-run the authentication request
   - Check token expiry time

---

## 📊 Expected Results

### Agent Server Responses:
- **Success**: Returns natural language response with API results
- **Error**: Returns error message with details
- **Processing Time**: 30-120 seconds (due to local LLM)

### Service Desk API Responses:
- **Auth**: Returns access token
- **Search**: Returns list of matching requests
- **Get**: Returns detailed request information
- **Create**: Returns created request with ID

---

## 🚀 Next Steps

1. **Test Basic Functionality**: Start with health check and examples
2. **Try Natural Language**: Test various ways to phrase requests
3. **Compare Results**: See how agent translates to actual API calls
4. **Extend Functionality**: Add new query types and API endpoints

The agent is designed to understand natural language and automatically determine which API to call with the correct parameters - exactly what you requested! 🎉
