# Complete Postman Setup Guide for API Agent

## 🚀 Server Status
✅ **API Agent Server is running at: http://localhost:5000**

## 📋 Quick Setup Checklist

### 1. **Import Postman Collection**
- Download: `api_agent/examples/API_Agent_Postman_Collection.json`
- Open Postman → Import → Select the JSON file
- Collection will appear with all pre-configured requests

### 2. **Set Environment Variables**
Create a new environment in Postman:
- `agent_server_url`: `http://localhost:5000`
- `service_desk_url`: `http://*************`
- `access_token`: (leave empty - will be auto-filled)

## 🎯 Testing the API Agent (Recommended)

### Step 1: Health Check
**Request:** `GET {{agent_server_url}}/health`

**Expected Response:**
```json
{
  "agent": "ready",
  "message": "API Agent server is running",
  "status": "healthy"
}
```

### Step 2: Get Example Queries
**Request:** `GET {{agent_server_url}}/examples`

**Expected Response:**
```json
{
  "examples": [
    {
      "description": "Search for open requests with high priority",
      "query": "Show me high priority open requests"
    }
  ]
}
```

### Step 3: Send Natural Language Query
**Request:** `POST {{agent_server_url}}/query`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "query": "Show me high priority open requests"
}
```

**Expected Response:**
```json
{
  "query": "Show me high priority open requests",
  "response": "I'll search for open requests with high priority for you...",
  "status": "success"
}
```

## 🔐 Authentication Management

The API agent handles authentication automatically, but here's how it works:

### Automatic Authentication
The agent automatically:
1. **Authenticates** with the service desk API using stored credentials
2. **Manages tokens** - refreshes when expired
3. **Handles errors** - re-authenticates if needed

### Manual Authentication (for direct API testing)

If you want to test the service desk API directly:

**Step 1: Get Access Token**
```
POST http://*************/api/oauth/token
```

**Headers:**
```
Authorization: Basic ZmxvdG8td2ViLWFwcDpjN3ByZE5KRVdFQmt4NGw3ZmV6bA==
Content-Type: application/x-www-form-urlencoded
```

**Body (form-urlencoded):**
```
username: <EMAIL>
password: 2d7QdRn6bMI1Q2vQBhficw==
grant_type: password
```

**Step 2: Use Token in Subsequent Requests**
```
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE
```

## 📝 Example Postman Requests

### 1. Basic Agent Query
```
POST http://localhost:5000/query
Content-Type: application/json

{
  "query": "Show me all open requests"
}
```

### 2. Specific Request Details
```
POST http://localhost:5000/query
Content-Type: application/json

{
  "query": "Get details of request ID 1"
}
```

### 3. Create New Request
```
POST http://localhost:5000/query
Content-Type: application/json

{
  "query": "Create a new request for printer issue with high priority"
}
```

### 4. Filter by Priority
```
POST http://localhost:5000/query
Content-Type: application/json

{
  "query": "Find all critical priority requests"
}
```

## 🧪 Testing Scenarios

### Scenario 1: Natural Language Variations
Test different ways to ask the same thing:

```json
{"query": "Show me high priority open tickets"}
{"query": "Find open requests with high priority"}
{"query": "Get all urgent open requests"}
{"query": "List high priority requests that are still open"}
```

### Scenario 2: Different Operations
```json
{"query": "Get details of request ID 5"}
{"query": "Create a new request for email not working"}
{"query": "Show me all closed requests"}
{"query": "Find requests with medium priority"}
```

### Scenario 3: Error Handling
```json
{"query": ""}
{"query": "Invalid nonsense query"}
{}
```

## ⏱️ Response Times

- **Health Check**: < 1 second
- **Examples**: < 1 second  
- **Natural Language Queries**: 30-120 seconds (local LLM processing)

## 🔧 Troubleshooting

### Server Not Responding
```bash
# Check if server is running
curl http://localhost:5000/health

# If not running, restart:
python start_api_server.py
```

### Authentication Issues
The agent handles auth automatically, but if you see auth errors:
1. Check if `http://*************` is accessible
2. Verify credentials in `api_agent/src/api_agent.py`
3. Check network connectivity

### Slow Responses
- Normal for local LLM (30-120 seconds)
- Agent is processing natural language and making API calls
- Check server logs for progress

## 📊 Expected Results

### Successful Query Response
```json
{
  "query": "Show me high priority open requests",
  "response": "Found requests:\n- ID: 1, Subject: Test, Status: 9\n- ID: 2, Subject: Printer Issue, Status: 2",
  "status": "success"
}
```

### Error Response
```json
{
  "query": "invalid query",
  "error": "Agent processing error: ...",
  "status": "agent_error"
}
```

## 🎯 Key Features Demonstrated

1. **Natural Language Understanding**: Agent interprets human language
2. **API Translation**: Converts to proper API calls automatically
3. **Authentication**: Handles OAuth tokens transparently
4. **Error Handling**: Graceful error responses
5. **Flexible Queries**: Multiple ways to ask the same thing

## 🚀 Next Steps

1. **Start with Health Check**: Verify server is working
2. **Try Example Queries**: Use the provided examples
3. **Test Natural Language**: Try different ways to phrase requests
4. **Monitor Responses**: Check how agent translates your queries
5. **Extend Functionality**: Add new query types as needed

Your API agent is now ready to convert natural language into API calls! 🎉
