# 🔐 Authentication Guide for API Agent

## ✅ **Server Status**
**Your API Agent Server is RUNNING at: http://localhost:5000**
**Authentication: AUTOMATIC** ✅

---

## 🎯 **How Authentication Works**

### **Automatic Authentication (Default)**
When you hit the `/query` endpoint, the agent **automatically**:
1. ✅ **Checks** if it has a valid token
2. ✅ **Authenticates** with service desk API if needed
3. ✅ **Gets access token** using stored credentials
4. ✅ **Makes API calls** with the token
5. ✅ **Returns results** to you

**You don't need to do anything!** 🎉

---

## 📱 **Postman Endpoints for Authentication**

### **1. Check Authentication Status**
```
GET http://localhost:5000/auth-status
```

**Response Example:**
```json
{
  "auto_authentication": true,
  "message": "Already authenticated with service desk API",
  "service_desk_url": "http://*************",
  "status": "authenticated",
  "token_available": true
}
```

### **2. Manual Authentication (Optional)**
```
POST http://localhost:5000/authenticate
```

**Response Example:**
```json
{
  "message": "Successfully authenticated with service desk API",
  "status": "success",
  "token_available": true
}
```

### **3. Natural Language Query (Auto-Auth)**
```
POST http://localhost:5000/query
Content-Type: application/json

{
  "query": "Show me high priority open requests"
}
```

**Response Example:**
```json
{
  "query": "Show me high priority open requests",
  "response": "Found requests:\n- ID: 1, Subject: Test, Status: 9",
  "status": "success",
  "authentication": {
    "status": "authenticated",
    "message": "Agent automatically handled authentication"
  }
}
```

---

## 🔄 **Authentication Flow**

### **When you send a query:**

1. **You:** Send POST to `/query` with natural language
2. **Agent:** Checks if authenticated
3. **Agent:** If not authenticated → automatically gets token
4. **Agent:** Processes your natural language
5. **Agent:** Calls service desk API with token
6. **Agent:** Returns formatted results + auth status

### **Visual Flow:**
```
You → /query → Agent checks auth → Auto-authenticate → Process query → API call → Results
```

---

## 🧪 **Testing Authentication in Postman**

### **Step 1: Import Collection**
- File: `api_agent/examples/API_Agent_Postman_Collection.json`
- Contains all authentication endpoints

### **Step 2: Test Authentication Flow**

#### **Test 1: Check Initial Status**
```
GET http://localhost:5000/auth-status
```
**Expected:** `"status": "not_authenticated"`

#### **Test 2: Manual Authentication**
```
POST http://localhost:5000/authenticate
```
**Expected:** `"status": "success"`

#### **Test 3: Check Status Again**
```
GET http://localhost:5000/auth-status
```
**Expected:** `"status": "authenticated"`

#### **Test 4: Query with Auto-Auth**
```
POST http://localhost:5000/query
{
  "query": "Show me open requests"
}
```
**Expected:** Response includes authentication status

---

## 🔧 **Authentication Configuration**

### **Credentials Location:**
File: `api_agent/src/api_agent.py`
```python
def authenticate(self, 
    username: str = "<EMAIL>", 
    password: str = "2d7QdRn6bMI1Q2vQBhficw=="
):
```

### **Service Desk URL:**
```python
self.base_url = "http://*************"
```

### **OAuth Endpoint:**
```
POST http://*************/api/oauth/token
```

---

## 🎯 **Key Benefits**

1. **🔄 Automatic:** No manual token management
2. **🔒 Secure:** Credentials stored securely in code
3. **🔄 Refresh:** Auto-refreshes expired tokens
4. **❌ Error Handling:** Graceful auth failure handling
5. **📊 Transparent:** Shows auth status in responses

---

## 🚨 **Troubleshooting**

### **Authentication Failed**
```json
{
  "status": "failed",
  "message": "Authentication failed - check credentials or network"
}
```

**Solutions:**
1. Check network access to `http://*************`
2. Verify credentials in `api_agent.py`
3. Check service desk API is running

### **Token Expired**
- **Automatic:** Agent will re-authenticate automatically
- **Manual:** Call `/authenticate` endpoint

### **Network Issues**
```bash
# Test connectivity
curl http://*************/api/oauth/token
```

---

## 📊 **Authentication Status Codes**

| Status | Meaning |
|--------|---------|
| `not_authenticated` | No token available yet |
| `authenticated` | Valid token available |
| `failed` | Authentication attempt failed |
| `error` | System error during auth |

---

## 🎉 **Summary**

### **For Regular Use:**
1. ✅ **Just use `/query`** - authentication is automatic
2. ✅ **No setup needed** - credentials are pre-configured
3. ✅ **No token management** - agent handles everything

### **For Debugging:**
1. 🔍 **Check `/auth-status`** - see current auth state
2. 🔄 **Use `/authenticate`** - manually trigger auth
3. 📊 **Monitor responses** - auth status included in query results

**Your API agent automatically handles authentication when you hit `/query`!** 🚀

---

## 📝 **Quick Test Commands**

```bash
# Check auth status
curl http://localhost:5000/auth-status

# Manual auth
curl -X POST http://localhost:5000/authenticate

# Query with auto-auth
curl -X POST http://localhost:5000/query \
  -H "Content-Type: application/json" \
  -d '{"query": "Show me open requests"}'
```

**Ready to test with Postman!** 🎯
