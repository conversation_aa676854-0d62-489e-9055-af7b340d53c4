#!/usr/bin/env python3
"""
API Agent Server Startup Script
===============================

This script starts the API agent server with proper path configuration.
"""

import os
import sys

# Add the api_agent source directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
api_agent_src = os.path.join(current_dir, 'api_agent', 'src')
sys.path.insert(0, api_agent_src)

# Now import and run the server
if __name__ == "__main__":
    try:
        from api_agent_server import app
        
        print("🚀 Starting API Agent Server...")
        print("📡 Server will be available at: http://localhost:5000")
        print("📚 Endpoints:")
        print("  - GET  /health     - Health check")
        print("  - GET  /examples   - Example queries")
        print("  - POST /query      - Send natural language queries")
        print("🔍 Use Postman to test the endpoints")
        print("=" * 60)
        
        # Create tmp directory if it doesn't exist
        os.makedirs("tmp", exist_ok=True)
        
        # Run the Flask server
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            threaded=True
        )
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("Make sure you're in the correct directory and have all dependencies installed.")
        print("Run: pip install -r api_agent/config/requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)
