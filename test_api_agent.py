#!/usr/bin/env python3
"""
Test script for the API Agent
=============================

This script demonstrates how the API agent can understand natural language
and translate it to API calls.
"""

from api_agent import create_api_agent
import os

def test_api_agent():
    """Test the API agent with various natural language queries"""
    
    print("🧪 Testing API Agent with Natural Language Queries...")
    print("=" * 60)
    
    # Create the agent
    agent = create_api_agent()
    
    # Test queries that demonstrate natural language understanding
    test_queries = [
        "Show me all open requests with high priority",
        "Find requests that have status open and medium priority", 
        "Get details of request ID 1",
        "Create a new request for 'Printer not working' with high priority",
        "List all closed requests",
        "Show me urgent requests",
        "Find all requests with critical priority",
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📝 Test Query {i}: {query}")
        print("-" * 50)
        try:
            agent.print_response(query, stream=True)
        except Exception as e:
            print(f"Error: {e}")
        print("\n" + "=" * 60)

def demo_api_understanding():
    """Demonstrate how the agent understands different ways to ask for the same thing"""
    
    print("🎯 Demonstrating Natural Language Understanding...")
    print("=" * 60)
    
    agent = create_api_agent()
    
    # Different ways to ask for the same thing
    similar_queries = [
        "Show me high priority open tickets",
        "Find open requests with high priority", 
        "Get all urgent open requests",
        "List high priority requests that are still open"
    ]
    
    print("These queries should all result in similar API calls:")
    for query in similar_queries:
        print(f"- '{query}'")
    
    print("\nLet's test the first one:")
    agent.print_response(similar_queries[0], stream=True)

if __name__ == "__main__":
    # Create tmp directory if it doesn't exist
    os.makedirs("tmp", exist_ok=True)
    
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        demo_api_understanding()
    else:
        test_api_agent()
