#!/bin/bash

# Setup script for Agno Agent with <PERSON>llama and Llama 3.2
# This script sets up everything needed to run the agent

echo "🚀 Setting up Agno Agent with Ollama and Llama 3.2..."
echo "=" * 60

# Check if Ollama is installed
if ! command -v ollama &> /dev/null; then
    echo "❌ Ollama is not installed!"
    echo "Please install Ollama first:"
    echo "  - Visit: https://ollama.com"
    echo "  - Or run: curl -fsSL https://ollama.com/install.sh | sh"
    exit 1
fi

echo "✅ Ollama is installed"

# Check if Ollama is running
if ! ollama list &> /dev/null; then
    echo "⚠️  Ollama service is not running. Starting Ollama..."
    # Try to start ollama in background
    ollama serve &
    sleep 3
fi

# Pull Llama 3.2 model if not already available
echo "📥 Checking for Llama 3.2 model..."
if ! ollama list | grep -q "llama3.2"; then
    echo "📥 Downloading Llama 3.2 model (this may take a while)..."
    ollama pull llama3.2
    if [ $? -eq 0 ]; then
        echo "✅ Llama 3.2 model downloaded successfully!"
    else
        echo "❌ Failed to download Llama 3.2 model"
        echo "You can try manually: ollama pull llama3.2"
        exit 1
    fi
else
    echo "✅ Llama 3.2 model is already available"
fi

# Install Python dependencies
echo "📦 Installing Python dependencies..."
if [ -f "requirements_ollama.txt" ]; then
    pip install -r requirements_ollama.txt
else
    echo "Installing core dependencies..."
    pip install agno ollama sentence-transformers lancedb torch
fi

if [ $? -eq 0 ]; then
    echo "✅ Python dependencies installed successfully!"
else
    echo "❌ Failed to install Python dependencies"
    exit 1
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p tmp

echo ""
echo "🎉 Setup complete!"
echo "=" * 60
echo "You can now run the agent with:"
echo "  python ollama_agent.py"
echo ""
echo "Or run demo questions with:"
echo "  python ollama_agent.py demo"
echo ""
echo "Available Ollama models:"
ollama list
