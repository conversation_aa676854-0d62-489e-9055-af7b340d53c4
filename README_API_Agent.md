# API Agent with Natural Language Understanding

This project creates an intelligent agent that can understand natural language requests and automatically translate them into API calls for your service desk system.

## 🎯 What It Does

The agent can understand requests like:
- **"Show me requests with status open and high priority"** → Calls search API with correct filters
- **"Get details of request ID 5"** → Calls get request API
- **"Create a new request for printer issue"** → Calls create request API
- **"Find all urgent tickets"** → Translates "urgent" to priority filter

## 🚀 Features

- **Natural Language Processing**: Understands various ways to express the same request
- **Automatic API Translation**: Converts natural language to correct API calls and payloads
- **Smart Mapping**: Maps human terms (urgent, high, open) to API values (IDs, status codes)
- **Error Handling**: Gracefully handles API errors and authentication issues
- **Session Memory**: Remembers conversation context
- **Local AI**: Uses Ollama with Llama 3.2 (no external API keys needed)

## 📋 Prerequisites

1. **Ollama with Llama 3.2** (from previous setup)
2. **Python 3.8+** with virtual environment
3. **Access to your service desk API** (IP: *************)

## 🛠️ Installation

1. **Install dependencies**:
   ```bash
   source .venv/bin/activate
   pip install -r requirements_api_agent.txt
   ```

2. **Configure API settings** (if needed):
   Edit `api_agent.py` to update:
   - Base URL (currently: `http://*************`)
   - Authentication credentials
   - Status/priority mappings

## 🎮 Usage

### Interactive Mode
```bash
python api_agent.py
```

### Test Mode
```bash
python test_api_agent.py
```

### Demo Mode
```bash
python test_api_agent.py demo
```

## 💬 Example Conversations

```
👤 You: Show me all open requests with high priority

🤖 Assistant: I'll search for open requests with high priority for you.

[Calls search_requests(status="open", priority="high")]

Found requests:
- ID: 1, Subject: Printer Issue, Status: 9
- ID: 3, Subject: Network Problem, Status: 2
- ID: 7, Subject: Software Bug, Status: 4
```

```
👤 You: Create a new request for email not working with critical priority

🤖 Assistant: I'll create a new request for the email issue with critical priority.

[Calls create_new_request(subject="email not working", priority="critical")]

Request created successfully! ID: 15, Subject: email not working
```

## 🔧 How It Works

### 1. Natural Language Understanding
The agent uses Llama 3.2 to understand user intent and extract:
- **Action**: search, get, create
- **Filters**: status, priority, ID
- **Parameters**: subject, description

### 2. API Mapping
The agent maps natural language terms to API values:

**Status Mapping**:
- "open" → excludes status ID 13 (closed)
- "closed" → status ID 13
- "new" → status ID 9
- "in_progress" → status IDs 2,3,4

**Priority Mapping**:
- "low" → urgencyId: 1
- "medium" → urgencyId: 2  
- "high" → urgencyId: 3
- "critical/urgent" → urgencyId: 4

### 3. API Execution
The agent automatically:
- Handles authentication (OAuth token)
- Builds correct payloads
- Makes HTTP requests
- Formats responses

## 🛠️ Customization

### Adding New API Endpoints
1. Add method to `ServiceDeskAPI` class
2. Add tool function to `APIAgentToolkit`
3. Register the tool in `__init__`

### Modifying Mappings
Update the mappings in `ServiceDeskAPI.__init__()`:
```python
self.status_mapping = {
    'your_status': [status_id_list],
    # ...
}
```

### Changing Base URL or Credentials
Edit the `ServiceDeskAPI.__init__()` method:
```python
def __init__(self, base_url: str = "http://your-api-server"):
    # ...
```

## 🔍 API Endpoints Supported

Based on your curl examples, the agent supports:

1. **Authentication**: `/api/oauth/token`
2. **Search Requests**: `/api/request/search/byqual`
3. **Get Request**: `/api/request/{id}`
4. **Create Request**: `/api/request`

## 🧪 Testing

The agent includes comprehensive testing:

```bash
# Test all functionality
python test_api_agent.py

# Test natural language understanding
python test_api_agent.py demo
```

## 🚨 Security Notes

- API credentials are handled securely
- SSL verification is disabled for internal APIs (`verify=False`)
- Tokens are managed automatically
- No sensitive data is logged

## 🔧 Troubleshooting

### Authentication Issues
- Check if the API server is accessible
- Verify credentials in `ServiceDeskAPI.authenticate()`
- Ensure the base64 encoded client credentials are correct

### API Mapping Issues
- Check status/priority mappings match your system
- Verify API endpoint URLs
- Test individual API calls with curl first

### Agent Understanding Issues
- The agent learns from conversation context
- Try rephrasing requests more clearly
- Check if the required tools are registered

## 🚀 Advanced Features

### Adding Context Awareness
The agent can remember previous requests:
```python
👤 You: Show me high priority requests
🤖 Assistant: [Shows results]
👤 You: Get details of the first one
🤖 Assistant: [Remembers the first request ID from previous results]
```

### Batch Operations
Extend the agent to handle multiple operations:
```python
👤 You: Create 3 requests for network issues with different priorities
🤖 Assistant: [Creates multiple requests automatically]
```

## 📈 Next Steps

1. **Add More Endpoints**: Support for updates, comments, attachments
2. **Enhanced Filtering**: Date ranges, assignee filters, custom fields
3. **Bulk Operations**: Create/update multiple requests at once
4. **Reporting**: Generate summaries and reports
5. **Integration**: Connect with other systems (email, Slack, etc.)

## 🤝 Contributing

To extend the agent:
1. Add new API methods to `ServiceDeskAPI`
2. Create corresponding tools in `APIAgentToolkit`
3. Update the agent instructions
4. Add test cases

The agent is designed to be easily extensible for your specific API needs!
