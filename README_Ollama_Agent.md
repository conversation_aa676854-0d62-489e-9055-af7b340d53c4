# Agno Agent with Ollama and Llama 3.2

This project demonstrates how to create a **Level 2 Agno Agent** using completely free and local tools:

- 🦙 **Ollama** with **Llama 3.2** model (runs locally, no API keys needed)
- 🧠 **Local embeddings** using Sentence Transformers
- 💾 **SQLite storage** for session management
- 📚 **Local knowledge base** with LanceDB vector database

## Features

- ✅ **100% Free**: No API keys or cloud services required
- 🏠 **Runs Locally**: All processing happens on your machine
- 🧠 **Memory & Knowledge**: Level 2 agent with persistent storage and knowledge base
- 💬 **Interactive Chat**: Real-time conversation with your local AI
- 📚 **Knowledge Search**: Agent can search its knowledge base before answering
- 🔄 **Session History**: Remembers conversation context

## Prerequisites

1. **Ollama installed** - Visit [ollama.com](https://ollama.com) to install
2. **Python 3.8+** with pip
3. **At least 4GB RAM** for running Llama 3.2

## Quick Setup

### Option 1: Automated Setup (Recommended)

```bash
# Run the setup script
./setup_ollama_agent.sh
```

### Option 2: Manual Setup

1. **Install Ollama** (if not already installed):
   ```bash
   curl -fsSL https://ollama.com/install.sh | sh
   ```

2. **Pull Llama 3.2 model**:
   ```bash
   ollama pull llama3.2
   ```

3. **Install Python dependencies**:
   ```bash
   pip install -r requirements_ollama.txt
   ```

4. **Create directories**:
   ```bash
   mkdir -p tmp
   ```

## Usage

### Interactive Chat Mode

```bash
python ollama_agent.py
```

This starts an interactive chat session where you can talk with your local Llama 3.2 model.

### Demo Mode

```bash
python ollama_agent.py demo
```

This runs a series of demo questions to showcase the agent's capabilities.

## Example Conversation

```
🤖 Agent ready! You can now chat with your local Llama 3.2 model.
============================================================

👤 You: What is Python programming?

🤖 Assistant:
----------------------------------------
Python is a high-level, interpreted programming language known for its 
simplicity and readability. Key features include dynamic typing, automatic 
memory management, and extensive standard library.

Python is widely used for:
- Web development
- Data science and machine learning
- Automation and scripting
- Desktop applications

👤 You: Write a simple function to add two numbers

🤖 Assistant:
----------------------------------------
Here's a simple Python function to add two numbers:

```python
def add_numbers(a, b):
    """
    Add two numbers and return the result.
    
    Args:
        a: First number
        b: Second number
    
    Returns:
        The sum of a and b
    """
    return a + b

# Example usage
result = add_numbers(5, 3)
print(f"5 + 3 = {result}")  # Output: 5 + 3 = 8
```
```

## Project Structure

```
.
├── ollama_agent.py           # Main agent script
├── requirements_ollama.txt   # Python dependencies
├── setup_ollama_agent.sh     # Automated setup script
├── README_Ollama_Agent.md    # This file
└── tmp/                      # Created automatically
    ├── lancedb_ollama/       # Vector database storage
    └── ollama_agent.db       # SQLite session storage
```

## Customization

### Using Different Models

You can easily switch to other Ollama models by modifying the `ollama_agent.py` file:

```python
# In the create_agent() function, change:
model=Ollama(id="llama3.2")

# To any other model, for example:
model=Ollama(id="llama3.2:1b")    # Smaller, faster version
model=Ollama(id="qwen2.5")        # Good for tool use
model=Ollama(id="deepseek-r1")    # Strong reasoning
model=Ollama(id="phi4")           # Small but powerful
```

### Adding Your Own Knowledge

Modify the `create_knowledge_base()` function in `ollama_agent.py` to add your own knowledge:

```python
knowledge_content = """
Your custom knowledge here...
Add information about your domain, company, or specific topics.
"""
```

### Adjusting Agent Behavior

Customize the agent's instructions in the `create_agent()` function:

```python
instructions=[
    "Your custom instructions here",
    "Define the agent's personality and behavior",
    "Add specific guidelines for responses",
]
```

## Troubleshooting

### Ollama Not Found
```bash
# Install Ollama
curl -fsSL https://ollama.com/install.sh | sh

# Or visit https://ollama.com for platform-specific instructions
```

### Model Not Downloaded
```bash
# Pull the model manually
ollama pull llama3.2

# Check available models
ollama list
```

### Memory Issues
If you encounter memory issues, try a smaller model:
```bash
ollama pull llama3.2:1b  # 1B parameter version (smaller)
```

### Dependencies Issues
```bash
# Reinstall dependencies
pip install --upgrade -r requirements_ollama.txt

# Or install individually
pip install agno ollama sentence-transformers lancedb
```

## Performance Tips

1. **Use smaller models** for faster responses: `llama3.2:1b` or `phi4`
2. **Increase RAM** allocation if possible
3. **Use SSD storage** for better vector database performance
4. **Close other applications** to free up memory

## What's Next?

This is a **Level 2 Agent** with knowledge and storage. You can extend it to:

- **Level 3**: Add reasoning capabilities with `ReasoningTools`
- **Level 4**: Create agent teams for complex tasks
- **Level 5**: Build agentic workflows with state management

Check out the [Agno documentation](https://docs.agno.com) for more advanced features!

## Contributing

Feel free to improve this example:
- Add more sophisticated knowledge bases
- Implement additional tools
- Create specialized agents for different domains
- Optimize performance

## License

This project is open source and available under the MIT License.
